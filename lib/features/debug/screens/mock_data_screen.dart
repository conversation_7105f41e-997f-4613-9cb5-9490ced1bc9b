import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:material_symbols_icons/symbols.dart';
import '../mock_data/mock_data.dart';
import '../mock_data/services/study_plan_upload_service.dart';
import '../mock_data/services/chat_upload_service.dart';
import '../../chat/models/chat_participant.dart';
import '../../chat/controllers/chat_testing_controller.dart';

class MockDataManagementScreen extends ConsumerStatefulWidget {
  const MockDataManagementScreen({super.key});

  @override
  ConsumerState<MockDataManagementScreen> createState() =>
      _MockDataManagementScreenState();
}

class _MockDataManagementScreenState
    extends ConsumerState<MockDataManagementScreen> {
  bool _isLoading = false;
  String? _lastAction;
  String? _lastResult;

  Future<void> _executeAction(
    String actionName,
    Future<void> Function() action,
  ) async {
    setState(() {
      _isLoading = true;
      _lastAction = actionName;
      _lastResult = null;
    });

    try {
      await action();
      setState(() {
        _lastResult = '✅ $actionName completed successfully';
      });
    } catch (e) {
      setState(() {
        _lastResult = '❌ $actionName failed: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      appBar: AppBar(title: const Text('Mock Data Management')),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Status Section
          if (_lastAction != null) ...[
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Last Action Status', style: textTheme.titleMedium),
                    const SizedBox(height: 8),
                    if (_isLoading)
                      Row(
                        children: [
                          const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          const SizedBox(width: 8),
                          Text('Executing: $_lastAction...'),
                        ],
                      )
                    else if (_lastResult != null)
                      Text(
                        _lastResult!,
                        style: TextStyle(
                          color: _lastResult!.startsWith('✅')
                              ? Colors.green
                              : Colors.red,
                        ),
                      ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
          ],

          // Data Overview Section
          _buildSection('Data Overview', [_buildDataOverviewCard(textTheme)]),
          const SizedBox(height: 24),

          // Upload Actions Section
          _buildSection('Upload Actions', [
            _buildActionButton(
              'Upload All Mock Data',
              'Upload classes, homework, and submissions to Firebase',
              Symbols.cloud_upload,
              Colors.blue,
              () => _executeAction('Upload All Mock Data', () async {
                final uploadService = MockDataUploadService();
                await uploadService.uploadAllMockData();
              }),
            ),
            const SizedBox(height: 12),
            _buildActionButton(
              'Upload Classes Only',
              'Upload classroom data to Firebase',
              Symbols.school,
              Colors.green,
              () => _executeAction('Upload Classes', () async {
                final uploadService = MockDataUploadService();
                await uploadService.uploadMockClasses();
              }),
            ),
            const SizedBox(height: 12),
            _buildActionButton(
              'Upload Homework Only',
              'Upload homework assignments to Firebase',
              Symbols.assignment,
              Colors.orange,
              () => _executeAction('Upload Homework', () async {
                final uploadService = MockDataUploadService();
                await uploadService.uploadMockHomework();
              }),
            ),
            const SizedBox(height: 12),
            _buildActionButton(
              'Upload Submissions Only',
              'Upload homework submissions to Firebase',
              Symbols.file_upload,
              Colors.purple,
              () => _executeAction('Upload Submissions', () async {
                final uploadService = MockDataUploadService();
                await uploadService.uploadMockSubmissions();
              }),
            ),
            const SizedBox(height: 12),
            _buildActionButton(
              'Upload Profiles Only',
              'Upload user profiles to Firebase',
              Symbols.person,
              Colors.indigo,
              () => _executeAction('Upload Profiles', () async {
                final uploader = ProfileUploadService();
                await uploader.uploadAllProfiles();
              }),
            ),
            const SizedBox(height: 12),
            _buildActionButton(
              'Upload Activities Only',
              'Upload classroom activities to Firebase',
              Symbols.feed,
              Colors.teal,
              () => _executeAction('Upload Activities', () async {
                final uploadService = MockDataUploadService();
                await uploadService.uploadMockActivities();
              }),
            ),
            const SizedBox(height: 12),
            _buildActionButton(
              'Upload Digital Library',
              'Upload library folders, files, and upload sessions',
              Symbols.folder,
              Colors.cyan,
              () => _executeAction('Upload Digital Library', () async {
                final uploader = DigitalLibraryUploadService();
                await uploader.uploadAllDigitalLibraryData();
              }),
            ),
            const SizedBox(height: 12),
            _buildActionButton(
              'Upload Announcements Only',
              'Upload announcements data to Firebase',
              Symbols.campaign,
              Colors.deepOrange,
              () => _executeAction('Upload Announcements', () async {
                final uploadService = MockDataUploadService();
                await uploadService.uploadMockAnnouncements();
              }),
            ),
            const SizedBox(height: 12),
            _buildActionButton(
              'Upload Study Plans Only',
              'Upload study plans, sections, tasks, and resources to Firebase',
              Symbols.schedule,
              Colors.deepPurple,
              () => _executeAction('Upload Study Plans', () async {
                final uploader = StudyPlanUploadService();
                await uploader.uploadAllStudyPlans();
              }),
            ),
            const SizedBox(height: 12),
            _buildActionButton(
              'Upload Chat Data',
              'Upload chats, messages, and participants to Firebase',
              Symbols.chat,
              Colors.indigo,
              () => _executeAction('Upload Chat Data', () async {
                // Create participants map from chat data
                final participantsMap = <String, List<ChatParticipant>>{};
                for (final chat in mockChatsList) {
                  participantsMap[chat.id] = chat.participants.values.toList();
                }

                await ChatUploadService.uploadAllChatData(
                  chats: mockChatsList,
                  messages: mockMessagesList,
                  participants: participantsMap,
                );
              }),
            ),
            const SizedBox(height: 12),
            _buildActionButton(
              'Test Chat Features',
              'Run comprehensive tests for chat functionality',
              Symbols.bug_report,
              Colors.orange,
              () => _executeAction('Test Chat Features', () async {
                final chatTesting = ref.read(chatTestingProvider.notifier);
                final results = await chatTesting.runAllTests();

                // Show results in a dialog
                if (mounted) {
                  _showTestResults(results);
                }
              }),
            ),
            const SizedBox(height: 12),
          ]),
          const SizedBox(height: 24),

          // Testing Actions Section
          _buildSection('Testing & Validation', [
            _buildActionButton(
              'Test Data Coverage',
              'Verify mock data meets all requirements',
              Symbols.verified,
              Colors.teal,
              () => _executeAction('Test Data Coverage', () async {
                MockDataValidator.testMockDataCoverage();
                MockDataValidator.testDataConsistency();
              }),
            ),
            const SizedBox(height: 12),
            _buildActionButton(
              'Test Profile Data',
              'Validate profile mock data requirements',
              Symbols.person_search,
              Colors.cyan,
              () => _executeAction('Test Profile Data', () async {
                final uploader = ProfileUploadService();
                final isValid = uploader.validateMockData();
                if (!isValid) {
                  throw Exception('Profile mock data validation failed');
                }
                final stats = uploader.getMockProfileStats();
                debugPrint('Profile Stats: $stats');
              }),
            ),
            const SizedBox(height: 12),
            _buildActionButton(
              'Validate All Data',
              'Run comprehensive validation on all mock data',
              Symbols.check_circle,
              Colors.green,
              () => _executeAction('Validate All Data', () async {
                final isValid = MockDataValidator.validateAllMockData();
                if (!isValid) {
                  throw Exception('Mock data validation failed');
                }
              }),
            ),
          ]),
          const SizedBox(height: 24),

          // Cleanup Actions Section
          _buildSection('Cleanup Actions', [
            _buildActionButton(
              'Clear All Mock Data',
              'Remove all mock data from Firebase (DESTRUCTIVE)',
              Symbols.delete_forever,
              Colors.red,
              () => _showClearDataDialog(),
            ),
          ]),
        ],
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: Theme.of(context).textTheme.headlineSmall),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }

  Widget _buildDataOverviewCard(TextTheme textTheme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Current Mock Data Statistics', style: textTheme.titleMedium),
            const SizedBox(height: 12),
            _buildStatRow('Classes', '${mockClassesList.length}'),
            _buildStatRow('Homework Items', '${mockHomeworkList.length}'),
            _buildStatRow('Submissions', '${mockHomeworkSubmissions.length}'),
            _buildStatRow('Profiles', '${mockProfilesList.length}'),
            _buildStatRow('Announcements', '${mockAnnouncementsList.length}'),
            _buildStatRow('Study Plans', '${mockStudyPlansList.length}'),
            _buildStatRow('Chats', '${mockChatsList.length}'),
            _buildStatRow('Messages', '${mockMessagesList.length}'),
            _buildStatRow(
              'Chat Participants',
              '${mockChatParticipantsList.length}',
            ),
            _buildStatRow('Library Folders', '${mockLibraryFolders.length}'),
            _buildStatRow('Library Files', '${mockLibraryFiles.length}'),
            _buildStatRow('Upload Sessions', '${mockUploadSessions.length}'),
            const SizedBox(height: 8),
            Text(
              'Current User ID: XRTanMcAUWSMq3mrRvve2Y9IMP12',
              style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(value, style: const TextStyle(fontWeight: FontWeight.w600)),
        ],
      ),
    );
  }

  Widget _buildActionButton(
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback? onPressed,
  ) {
    return Card(
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.withValues(alpha: 0.1),
          child: Icon(icon, color: color),
        ),
        title: Text(title),
        subtitle: Text(description),
        trailing: _isLoading ? null : const Icon(Symbols.arrow_forward_ios),
        onTap: _isLoading ? null : onPressed,
        enabled: !_isLoading,
      ),
    );
  }

  void _showClearDataDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Mock Data'),
        content: const Text(
          'This will permanently delete all mock data from Firebase. '
          'This action cannot be undone. Are you sure?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _executeAction('Clear All Mock Data', () async {
                final uploadService = MockDataUploadService();
                await uploadService.clearAllMockData();
              });
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showTestResults(Map<String, dynamic> results) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Chat Feature Test Results'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (results['summary'] != null) ...[
                  Text(
                    'Summary',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Text('Passed: ${results['summary']['passed'] ?? 0}'),
                  Text('Failed: ${results['summary']['failed'] ?? 0}'),
                  Text('Total: ${results['summary']['total'] ?? 0}'),
                  const SizedBox(height: 16),
                ],
                if (results['tests'] != null) ...[
                  Text(
                    'Test Details',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  ...((results['tests'] as Map<String, dynamic>).entries.map(
                    (entry) => Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      child: Row(
                        children: [
                          Icon(
                            entry.value['success'] == true
                                ? Icons.check_circle
                                : Icons.error,
                            color: entry.value['success'] == true
                                ? Colors.green
                                : Colors.red,
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              '${entry.key}: ${entry.value['message'] ?? 'No message'}',
                              style: const TextStyle(fontSize: 12),
                            ),
                          ),
                        ],
                      ),
                    ),
                  )),
                ],
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

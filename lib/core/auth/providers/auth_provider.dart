import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../enums/auth_enums.dart' as auth_enums;
import '../enums/auth_status.dart';
import '../models/app_user.dart';
import '../models/auth_error.dart';
import '../models/auth_state.dart';
import 'auth_notifier.dart';

/// Main authentication state provider
final authStateProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier(ref);
});

/// Provider for the current authenticated user
final currentUserProvider = Provider<AppUser?>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.user;
});

/// Provider to check if user is authenticated
final isAuthenticatedProvider = Provider<bool>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.isAuthenticated;
});

/// Provider to check if user can access the main app
final canAccessAppProvider = Provider<bool>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.canAccessApp;
});

/// Provider to check if authentication is loading
final isAuthLoadingProvider = Provider<bool>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.isLoadingState;
});

/// Provider for current authentication error
final authErrorProvider = Provider<AuthError?>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.error;
});

/// Provider to check if there's an authentication error
final hasAuthErrorProvider = Provider<bool>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.hasError;
});

/// Provider for current authentication status
final authStatusProvider = Provider<AuthStatus>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.status;
});

/// Provider to check if user requires email verification
final requiresEmailVerificationProvider = Provider<bool>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.status == AuthStatus.requiresEmailVerification;
});

/// Provider to check if user requires profile setup
final requiresProfileSetupProvider = Provider<bool>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.status == AuthStatus.requiresProfileSetup;
});

/// Provider to check if user requires any action
final requiresActionProvider = Provider<bool>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.requiresAction;
});

/// Provider for user's display name
final userDisplayNameProvider = Provider<String?>((ref) {
  final user = ref.watch(currentUserProvider);
  return user?.effectiveDisplayName;
});

/// Provider for user's email
final userEmailProvider = Provider<String?>((ref) {
  final user = ref.watch(currentUserProvider);
  return user?.email;
});

/// Provider for user's profile image URL
final userProfileImageProvider = Provider<String?>((ref) {
  final user = ref.watch(currentUserProvider);
  return user?.effectiveProfileImageUrl;
});

/// Provider to check if user's email is verified
final isEmailVerifiedProvider = Provider<bool>((ref) {
  final user = ref.watch(currentUserProvider);
  return user?.emailVerified ?? false;
});

/// Provider to check if user has completed profile
final hasCompletedProfileProvider = Provider<bool>((ref) {
  final user = ref.watch(currentUserProvider);
  return user?.hasCompletedProfile ?? false;
});

/// Provider for user's type
final userTypeProvider = Provider<String?>((ref) {
  final user = ref.watch(currentUserProvider);
  return user?.userType.displayName;
});

/// Provider for authentication state message
final authMessageProvider = Provider<String>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.message;
});

/// Provider to check if current operation can be retried
final canRetryAuthOperationProvider = Provider<bool>((ref) {
  final authError = ref.watch(authErrorProvider);
  return authError?.canRetry ?? false;
});

/// Provider for redirect URL after authentication
final authRedirectUrlProvider = Provider<String?>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.redirectUrl;
});

/// Extension methods for AuthNotifier to provide convenient access
extension AuthNotifierExtension on WidgetRef {
  /// Get the AuthNotifier instance
  AuthNotifier get auth => read(authStateProvider.notifier);

  /// Sign in with email and password
  Future<void> signIn({required String email, required String password}) {
    return auth.signInWithEmailAndPassword(email: email, password: password);
  }

  /// Sign up with email and password
  Future<void> signUp({
    required String email,
    required String password,
    String? displayName,
  }) {
    return auth.signUpWithEmailAndPassword(
      email: email,
      password: password,
      displayName: displayName,
    );
  }

  /// Sign out current user
  Future<void> signOut() {
    return auth.signOut();
  }

  /// Send password reset email
  Future<void> resetPassword({required String email}) {
    return auth.sendPasswordResetEmail(email: email);
  }

  /// Send email verification
  Future<void> verifyEmail() {
    return auth.sendEmailVerification();
  }

  /// Reload current user
  Future<void> reloadUser() {
    return auth.reloadUser();
  }

  /// Clear authentication error
  void clearAuthError() {
    auth.clearError();
  }
}

/// Helper methods for checking authentication state
extension AuthStateHelpers on WidgetRef {
  /// Check if user is signed in
  bool get isSignedIn => read(isAuthenticatedProvider);

  /// Check if user can access app
  bool get canAccessApp => read(canAccessAppProvider);

  /// Check if auth is loading
  bool get isAuthLoading => read(isAuthLoadingProvider);

  /// Check if there's an auth error
  bool get hasAuthError => read(hasAuthErrorProvider);

  /// Get current user
  AppUser? get currentUser => read(currentUserProvider);

  /// Get auth error
  AuthError? get authError => read(authErrorProvider);

  /// Get auth status
  AuthStatus get authStatus => read(authStatusProvider);
}

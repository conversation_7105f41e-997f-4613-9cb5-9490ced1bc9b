import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../../enums/auth_enums.dart' as auth_enums;
import '../../services/local_storage_service.dart';
import '../enums/auth_status.dart';
import '../models/app_user.dart';
import '../models/auth_error.dart';
import '../models/auth_state.dart';
import '../services/auth_service.dart';

/// Unified authentication state notifier that manages the complete auth flow
class AuthNotifier extends StateNotifier<AuthState> {
  AuthNotifier(this._ref) : super(AuthState.initial()) {
    _initialize();
  }

  final Ref _ref;
  final Logger _logger = Logger();
  StreamSubscription<User?>? _authStateSubscription;

  /// Initialize the authentication system
  Future<void> _initialize() async {
    try {
      _logger.i('Initializing authentication system');

      // Set initial loading state
      state = AuthState.loading();

      // Try to load saved user data from local storage
      await _loadSavedUserData();

      // Listen to Firebase auth state changes
      _listenToAuthStateChanges();

      _logger.i('Authentication system initialized successfully');
    } catch (e) {
      _logger.e('Failed to initialize authentication system: $e');
      state = AuthState.error(AuthError.fromException(Exception(e.toString())));
    }
  }

  /// Load saved user data from local storage
  Future<void> _loadSavedUserData() async {
    try {
      final localStorageService = LocalStorageService();
      final savedUser = await localStorageService.getUserData();

      if (savedUser != null) {
        _logger.i('Found saved user data for: ${savedUser.email}');

        // Convert saved UserModel to AppUser
        final appUser = AppUser(
          id: savedUser.id,
          email: savedUser.email,
          displayName: savedUser.displayName,
          photoUrl: savedUser.photoUrl,
          emailVerified: savedUser.emailVerified,
          createdAt: savedUser.createdAt,
          lastSignInAt: savedUser.lastSignInAt,
          phoneNumber: savedUser.phoneNumber,
          fullName: savedUser.fullName,
          userType: savedUser.userType,
          primaryClassId: savedUser.primaryClassId,
          profileImageUrl: savedUser.profileImageUrl,
          grade: savedUser.grade,
          school: savedUser.school,
          studentId: savedUser.studentId,
          bio: savedUser.bio,
          subjects: savedUser.subjects,
          chatPermission: savedUser.chatPermission,
          isChatAvailable: savedUser.isChatAvailable,
          approvedChatUsers: savedUser.approvedChatUsers,
        );

        // Set appropriate state based on user status
        if (!appUser.emailVerified) {
          state = AuthState.requiresEmailVerification(appUser);
        } else if (appUser.needsProfileSetup) {
          state = AuthState.requiresProfileSetup(appUser);
        } else {
          state = AuthState.authenticated(appUser);
        }
      } else {
        _logger.i('No saved user data found');
      }
    } catch (e) {
      _logger.e('Error loading saved user data: $e');
      // Don't throw error here, just continue with normal flow
    }
  }

  /// Listen to Firebase authentication state changes
  void _listenToAuthStateChanges() {
    final authService = AuthService();

    _authStateSubscription = authService.authStateChanges.listen(
      (User? firebaseUser) {
        _logger.i('Auth state changed: ${firebaseUser?.email ?? 'No user'}');
        _handleAuthStateChange(firebaseUser);
      },
      onError: (error) {
        _logger.e('Error in auth state stream: $error');
        state = AuthState.error(
          AuthError.fromException(Exception(error.toString())),
        );
      },
    );
  }

  /// Handle Firebase auth state changes
  void _handleAuthStateChange(User? firebaseUser) {
    if (firebaseUser != null) {
      final appUser = AppUser.fromFirebaseUser(firebaseUser);

      // Determine appropriate state based on user status
      if (!appUser.emailVerified) {
        state = AuthState.requiresEmailVerification(appUser);
      } else if (appUser.needsProfileSetup) {
        state = AuthState.requiresProfileSetup(appUser);
      } else {
        state = AuthState.authenticated(appUser);
      }

      // Save user data locally
      _saveUserDataLocally(appUser);
    } else {
      state = AuthState.unauthenticated();
      _clearUserDataLocally();
    }
  }

  /// Sign in with email and password
  Future<void> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      _logger.i('Starting sign in process for: $email');

      // Set loading state
      state = state.copyWith(
        status: AuthStatus.loading,
        isLoading: true,
        currentOperation: auth_enums.AuthOperation.signIn,
        clearError: true,
      );

      final authService = AuthService();
      final user = await authService.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      _logger.i('Sign in successful for: ${user.email}');
      // State will be updated automatically by the auth state stream
    } catch (e) {
      _logger.e('Sign in failed: $e');

      final authError = e is AuthError
          ? e
          : AuthError.fromException(
              Exception(e.toString()),
              operation: auth_enums.AuthOperation.signIn,
            );

      state = AuthState.error(authError);
    }
  }

  /// Sign up with email and password
  Future<void> signUpWithEmailAndPassword({
    required String email,
    required String password,
    String? displayName,
  }) async {
    try {
      _logger.i('Starting sign up process for: $email');

      // Set loading state
      state = state.copyWith(
        status: AuthStatus.loading,
        isLoading: true,
        currentOperation: auth_enums.AuthOperation.signUp,
        clearError: true,
      );

      final authService = AuthService();
      final user = await authService.signUpWithEmailAndPassword(
        email: email,
        password: password,
        displayName: displayName,
      );

      _logger.i('Sign up successful for: ${user.email}');
      // State will be updated automatically by the auth state stream
    } catch (e) {
      _logger.e('Sign up failed: $e');

      final authError = e is AuthError
          ? e
          : AuthError.fromException(
              Exception(e.toString()),
              operation: auth_enums.AuthOperation.signUp,
            );

      state = AuthState.error(authError);
    }
  }

  /// Sign out the current user
  Future<void> signOut() async {
    try {
      _logger.i('Starting sign out process');

      // Set loading state
      state = state.copyWith(
        status: AuthStatus.loading,
        isLoading: true,
        currentOperation: auth_enums.AuthOperation.signOut,
        clearError: true,
      );

      final authService = AuthService();
      await authService.signOut();

      _logger.i('Sign out successful');
      // State will be updated automatically by the auth state stream
    } catch (e) {
      _logger.e('Sign out failed: $e');

      final authError = e is AuthError
          ? e
          : AuthError.fromException(
              Exception(e.toString()),
              operation: auth_enums.AuthOperation.signOut,
            );

      state = AuthState.error(authError);
    }
  }

  /// Send password reset email
  Future<void> sendPasswordResetEmail({required String email}) async {
    try {
      _logger.i('Sending password reset email to: $email');

      // Set loading state
      state = state.copyWith(
        status: AuthStatus.loading,
        isLoading: true,
        currentOperation: auth_enums.AuthOperation.resetPassword,
        clearError: true,
      );

      final authService = AuthService();
      await authService.sendPasswordResetEmail(email: email);

      _logger.i('Password reset email sent successfully');

      // Return to previous state
      state = state.copyWith(
        status: AuthStatus.unauthenticated,
        isLoading: false,
        currentOperation: null,
      );
    } catch (e) {
      _logger.e('Password reset failed: $e');

      final authError = e is AuthError
          ? e
          : AuthError.fromException(
              Exception(e.toString()),
              operation: auth_enums.AuthOperation.resetPassword,
            );

      state = AuthState.error(authError);
    }
  }

  /// Send email verification
  Future<void> sendEmailVerification() async {
    try {
      _logger.i('Sending email verification');

      final authService = AuthService();
      await authService.sendEmailVerification();

      _logger.i('Email verification sent successfully');
    } catch (e) {
      _logger.e('Email verification failed: $e');

      final authError = e is AuthError
          ? e
          : AuthError.fromException(
              Exception(e.toString()),
              operation: auth_enums.AuthOperation.verifyEmail,
            );

      state = AuthState.error(authError);
    }
  }

  /// Reload current user data
  Future<void> reloadUser() async {
    try {
      final authService = AuthService();
      final reloadedUser = await authService.reloadUser();

      if (reloadedUser != null) {
        _handleAuthStateChange(AuthService().currentFirebaseUser);
      }
    } catch (e) {
      _logger.e('Error reloading user: $e');
    }
  }

  /// Clear any current error
  void clearError() {
    if (state.hasError) {
      state = state.copyWith(clearError: true);
    }
  }

  /// Save user data to local storage
  Future<void> _saveUserDataLocally(AppUser user) async {
    try {
      final localStorageService = LocalStorageService();

      // Convert AppUser to UserModel for compatibility with existing storage
      final userModel = user.toJson();
      await localStorageService.saveUserData(userModel as dynamic);

      _logger.i('User data saved locally for: ${user.email}');
    } catch (e) {
      _logger.e('Failed to save user data locally: $e');
    }
  }

  /// Clear user data from local storage
  Future<void> _clearUserDataLocally() async {
    try {
      final localStorageService = LocalStorageService();
      await localStorageService.clearUserData();

      _logger.i('User data cleared locally');
    } catch (e) {
      _logger.e('Failed to clear user data locally: $e');
    }
  }

  @override
  void dispose() {
    _authStateSubscription?.cancel();
    super.dispose();
  }
}

import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:logger/logger.dart';

import '../../enums/auth_enums.dart';
import '../models/app_user.dart';
import '../models/auth_error.dart';

/// Simplified authentication service that handles Firebase Auth operations
/// with proper error handling and logging
class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;
  final Logger _logger = Logger();

  /// Stream of authentication state changes
  Stream<User?> get authStateChanges => _firebaseAuth.authStateChanges();

  /// Stream of user changes (includes profile updates)
  Stream<User?> get userChanges => _firebaseAuth.userChanges();

  /// Get current Firebase user
  User? get currentFirebaseUser => _firebaseAuth.currentUser;

  /// Get current user as AppUser (basic Firebase data only)
  AppUser? get currentUser {
    final firebaseUser = currentFirebaseUser;
    if (firebaseUser == null) return null;
    return AppUser.fromFirebaseUser(firebaseUser);
  }

  /// Sign in with email and password
  Future<AppUser> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      _logger.i('Attempting to sign in user with email: $email');
      
      final credential = await _firebaseAuth.signInWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );

      if (credential.user == null) {
        _logger.e('Sign in failed: No user returned from Firebase');
        throw AuthError.custom(
          type: AuthErrorType.unknown,
          customMessage: 'Sign in failed. Please try again.',
          operation: AuthOperation.signIn,
        );
      }

      final user = AppUser.fromFirebaseUser(credential.user!);
      _logger.i('Sign in successful for: ${user.email}');
      
      return user;
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase Auth error during sign in: ${e.code} - ${e.message}');
      throw AuthError.fromException(
        Exception('firebase_auth/${e.code}: ${e.message}'),
        operation: AuthOperation.signIn,
      );
    } catch (e) {
      _logger.e('Unexpected error during sign in: $e');
      throw AuthError.fromException(
        Exception(e.toString()),
        operation: AuthOperation.signIn,
      );
    }
  }

  /// Sign up with email and password
  Future<AppUser> signUpWithEmailAndPassword({
    required String email,
    required String password,
    String? displayName,
  }) async {
    try {
      _logger.i('Attempting to sign up user with email: $email');
      
      final credential = await _firebaseAuth.createUserWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );

      if (credential.user == null) {
        _logger.e('Sign up failed: No user returned from Firebase');
        throw AuthError.custom(
          type: AuthErrorType.unknown,
          customMessage: 'Sign up failed. Please try again.',
          operation: AuthOperation.signUp,
        );
      }

      // Update display name if provided
      if (displayName?.isNotEmpty == true) {
        await credential.user!.updateDisplayName(displayName);
        await credential.user!.reload();
      }

      final user = AppUser.fromFirebaseUser(credential.user!);
      _logger.i('Sign up successful for: ${user.email}');
      
      return user;
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase Auth error during sign up: ${e.code} - ${e.message}');
      throw AuthError.fromException(
        Exception('firebase_auth/${e.code}: ${e.message}'),
        operation: AuthOperation.signUp,
      );
    } catch (e) {
      _logger.e('Unexpected error during sign up: $e');
      throw AuthError.fromException(
        Exception(e.toString()),
        operation: AuthOperation.signUp,
      );
    }
  }

  /// Sign out the current user
  Future<void> signOut() async {
    try {
      _logger.i('Attempting to sign out current user');
      await _firebaseAuth.signOut();
      _logger.i('Sign out successful');
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase Auth error during sign out: ${e.code} - ${e.message}');
      throw AuthError.fromException(
        Exception('firebase_auth/${e.code}: ${e.message}'),
        operation: AuthOperation.signOut,
      );
    } catch (e) {
      _logger.e('Unexpected error during sign out: $e');
      throw AuthError.fromException(
        Exception(e.toString()),
        operation: AuthOperation.signOut,
      );
    }
  }

  /// Send password reset email
  Future<void> sendPasswordResetEmail({required String email}) async {
    try {
      _logger.i('Sending password reset email to: $email');
      await _firebaseAuth.sendPasswordResetEmail(email: email.trim());
      _logger.i('Password reset email sent successfully');
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase Auth error sending password reset: ${e.code} - ${e.message}');
      throw AuthError.fromException(
        Exception('firebase_auth/${e.code}: ${e.message}'),
        operation: AuthOperation.resetPassword,
      );
    } catch (e) {
      _logger.e('Unexpected error sending password reset: $e');
      throw AuthError.fromException(
        Exception(e.toString()),
        operation: AuthOperation.resetPassword,
      );
    }
  }

  /// Send email verification to current user
  Future<void> sendEmailVerification() async {
    try {
      final user = currentFirebaseUser;
      if (user == null) {
        throw AuthError.custom(
          type: AuthErrorType.userNotFound,
          customMessage: 'No user is currently signed in.',
          operation: AuthOperation.verifyEmail,
        );
      }

      if (user.emailVerified) {
        _logger.i('Email is already verified for user: ${user.email}');
        return;
      }

      _logger.i('Sending email verification to: ${user.email}');
      await user.sendEmailVerification();
      _logger.i('Email verification sent successfully');
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase Auth error sending email verification: ${e.code} - ${e.message}');
      throw AuthError.fromException(
        Exception('firebase_auth/${e.code}: ${e.message}'),
        operation: AuthOperation.verifyEmail,
      );
    } catch (e) {
      _logger.e('Unexpected error sending email verification: $e');
      throw AuthError.fromException(
        Exception(e.toString()),
        operation: AuthOperation.verifyEmail,
      );
    }
  }

  /// Update user password
  Future<void> updatePassword({required String newPassword}) async {
    try {
      final user = currentFirebaseUser;
      if (user == null) {
        throw AuthError.custom(
          type: AuthErrorType.userNotFound,
          customMessage: 'No user is currently signed in.',
          operation: AuthOperation.updatePassword,
        );
      }

      _logger.i('Updating password for user: ${user.email}');
      await user.updatePassword(newPassword);
      _logger.i('Password updated successfully');
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase Auth error updating password: ${e.code} - ${e.message}');
      throw AuthError.fromException(
        Exception('firebase_auth/${e.code}: ${e.message}'),
        operation: AuthOperation.updatePassword,
      );
    } catch (e) {
      _logger.e('Unexpected error updating password: $e');
      throw AuthError.fromException(
        Exception(e.toString()),
        operation: AuthOperation.updatePassword,
      );
    }
  }

  /// Update user display name
  Future<AppUser> updateDisplayName({required String displayName}) async {
    try {
      final user = currentFirebaseUser;
      if (user == null) {
        throw AuthError.custom(
          type: AuthErrorType.userNotFound,
          customMessage: 'No user is currently signed in.',
          operation: AuthOperation.updateProfile,
        );
      }

      _logger.i('Updating display name for user: ${user.email}');
      await user.updateDisplayName(displayName);
      await user.reload();
      
      final updatedUser = AppUser.fromFirebaseUser(_firebaseAuth.currentUser!);
      _logger.i('Display name updated successfully');
      
      return updatedUser;
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase Auth error updating display name: ${e.code} - ${e.message}');
      throw AuthError.fromException(
        Exception('firebase_auth/${e.code}: ${e.message}'),
        operation: AuthOperation.updateProfile,
      );
    } catch (e) {
      _logger.e('Unexpected error updating display name: $e');
      throw AuthError.fromException(
        Exception(e.toString()),
        operation: AuthOperation.updateProfile,
      );
    }
  }

  /// Reload current user data
  Future<AppUser?> reloadUser() async {
    try {
      final user = currentFirebaseUser;
      if (user == null) return null;

      _logger.i('Reloading user data for: ${user.email}');
      await user.reload();
      final reloadedUser = AppUser.fromFirebaseUser(_firebaseAuth.currentUser!);
      _logger.i('Successfully reloaded user data');
      
      return reloadedUser;
    } catch (e) {
      _logger.e('Error reloading user data: $e');
      return null;
    }
  }

  /// Delete current user account
  Future<void> deleteAccount() async {
    try {
      final user = currentFirebaseUser;
      if (user == null) {
        throw AuthError.custom(
          type: AuthErrorType.userNotFound,
          customMessage: 'No user is currently signed in.',
          operation: AuthOperation.deleteAccount,
        );
      }

      _logger.i('Deleting account for user: ${user.email}');
      await user.delete();
      _logger.i('Account deleted successfully');
    } on FirebaseAuthException catch (e) {
      _logger.e('Firebase Auth error deleting account: ${e.code} - ${e.message}');
      throw AuthError.fromException(
        Exception('firebase_auth/${e.code}: ${e.message}'),
        operation: AuthOperation.deleteAccount,
      );
    } catch (e) {
      _logger.e('Unexpected error deleting account: $e');
      throw AuthError.fromException(
        Exception(e.toString()),
        operation: AuthOperation.deleteAccount,
      );
    }
  }
}

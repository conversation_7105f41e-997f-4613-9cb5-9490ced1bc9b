/// Enum representing different user account statuses
enum UserStatus {
  /// User account is pending activation (newly created)
  pending,
  
  /// User account is active and can access the app
  active,
  
  /// User account is temporarily suspended
  suspended,
  
  /// User account is permanently disabled
  disabled,
  
  /// User account requires email verification
  emailVerificationRequired,
  
  /// User account requires profile completion
  profileIncomplete,
}

/// Extension to provide additional functionality for UserStatus enum
extension UserStatusExtension on UserStatus {
  /// Get a user-friendly display name for the user status
  String get displayName {
    switch (this) {
      case UserStatus.pending:
        return 'Pending';
      case UserStatus.active:
        return 'Active';
      case UserStatus.suspended:
        return 'Suspended';
      case UserStatus.disabled:
        return 'Disabled';
      case UserStatus.emailVerificationRequired:
        return 'Email Verification Required';
      case UserStatus.profileIncomplete:
        return 'Profile Incomplete';
    }
  }

  /// Get the string value for the user status (for JSON serialization)
  String get value {
    switch (this) {
      case UserStatus.pending:
        return 'pending';
      case UserStatus.active:
        return 'active';
      case UserStatus.suspended:
        return 'suspended';
      case UserStatus.disabled:
        return 'disabled';
      case UserStatus.emailVerificationRequired:
        return 'email_verification_required';
      case UserStatus.profileIncomplete:
        return 'profile_incomplete';
    }
  }

  /// Get a description for the user status
  String get description {
    switch (this) {
      case UserStatus.pending:
        return 'Account is pending activation';
      case UserStatus.active:
        return 'Account is active and fully functional';
      case UserStatus.suspended:
        return 'Account is temporarily suspended';
      case UserStatus.disabled:
        return 'Account is permanently disabled';
      case UserStatus.emailVerificationRequired:
        return 'Email verification is required to activate account';
      case UserStatus.profileIncomplete:
        return 'Profile setup needs to be completed';
    }
  }

  /// Check if the user can access the app with this status
  bool get canAccessApp {
    switch (this) {
      case UserStatus.active:
        return true;
      case UserStatus.pending:
      case UserStatus.suspended:
      case UserStatus.disabled:
      case UserStatus.emailVerificationRequired:
      case UserStatus.profileIncomplete:
        return false;
    }
  }

  /// Check if the user can sign in with this status
  bool get canSignIn {
    switch (this) {
      case UserStatus.active:
      case UserStatus.profileIncomplete:
      case UserStatus.emailVerificationRequired:
        return true;
      case UserStatus.pending:
      case UserStatus.suspended:
      case UserStatus.disabled:
        return false;
    }
  }

  /// Check if this status requires immediate action from the user
  bool get requiresAction {
    switch (this) {
      case UserStatus.emailVerificationRequired:
      case UserStatus.profileIncomplete:
        return true;
      case UserStatus.pending:
      case UserStatus.active:
      case UserStatus.suspended:
      case UserStatus.disabled:
        return false;
    }
  }

  /// Create UserStatus from string value
  static UserStatus fromString(String value) {
    switch (value.toLowerCase()) {
      case 'pending':
        return UserStatus.pending;
      case 'active':
        return UserStatus.active;
      case 'suspended':
        return UserStatus.suspended;
      case 'disabled':
        return UserStatus.disabled;
      case 'email_verification_required':
        return UserStatus.emailVerificationRequired;
      case 'profile_incomplete':
        return UserStatus.profileIncomplete;
      default:
        return UserStatus.pending; // Default to pending for safety
    }
  }
}

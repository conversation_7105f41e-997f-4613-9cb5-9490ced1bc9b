/// Enum representing the current authentication state of the app
enum AuthStatus {
  /// User is not authenticated
  unauthenticated,
  
  /// User is authenticated and can access the app
  authenticated,
  
  /// Authentication state is being determined (loading)
  loading,
  
  /// An error occurred during authentication
  error,
  
  /// User is authenticated but requires email verification
  requiresEmailVerification,
  
  /// User is authenticated but profile setup is incomplete
  requiresProfileSetup,
}

/// Extension to provide additional functionality for AuthStatus enum
extension AuthStatusExtension on AuthStatus {
  /// Get a user-friendly display name for the auth status
  String get displayName {
    switch (this) {
      case AuthStatus.unauthenticated:
        return 'Not Signed In';
      case AuthStatus.authenticated:
        return 'Signed In';
      case AuthStatus.loading:
        return 'Loading';
      case AuthStatus.error:
        return 'Error';
      case AuthStatus.requiresEmailVerification:
        return 'Email Verification Required';
      case AuthStatus.requiresProfileSetup:
        return 'Profile Setup Required';
    }
  }

  /// Get the string value for the auth status (for JSON serialization)
  String get value {
    switch (this) {
      case AuthStatus.unauthenticated:
        return 'unauthenticated';
      case AuthStatus.authenticated:
        return 'authenticated';
      case AuthStatus.loading:
        return 'loading';
      case AuthStatus.error:
        return 'error';
      case AuthStatus.requiresEmailVerification:
        return 'requires_email_verification';
      case AuthStatus.requiresProfileSetup:
        return 'requires_profile_setup';
    }
  }

  /// Check if the user is considered authenticated (regardless of additional requirements)
  bool get isAuthenticated {
    switch (this) {
      case AuthStatus.authenticated:
      case AuthStatus.requiresEmailVerification:
      case AuthStatus.requiresProfileSetup:
        return true;
      case AuthStatus.unauthenticated:
      case AuthStatus.loading:
      case AuthStatus.error:
        return false;
    }
  }

  /// Check if the user can access the main app
  bool get canAccessApp {
    switch (this) {
      case AuthStatus.authenticated:
        return true;
      case AuthStatus.unauthenticated:
      case AuthStatus.loading:
      case AuthStatus.error:
      case AuthStatus.requiresEmailVerification:
      case AuthStatus.requiresProfileSetup:
        return false;
    }
  }

  /// Check if this status requires user action
  bool get requiresAction {
    switch (this) {
      case AuthStatus.requiresEmailVerification:
      case AuthStatus.requiresProfileSetup:
        return true;
      case AuthStatus.unauthenticated:
      case AuthStatus.authenticated:
      case AuthStatus.loading:
      case AuthStatus.error:
        return false;
    }
  }

  /// Check if this is a loading state
  bool get isLoading {
    return this == AuthStatus.loading;
  }

  /// Check if this is an error state
  bool get isError {
    return this == AuthStatus.error;
  }

  /// Create AuthStatus from string value
  static AuthStatus fromString(String value) {
    switch (value.toLowerCase()) {
      case 'unauthenticated':
        return AuthStatus.unauthenticated;
      case 'authenticated':
        return AuthStatus.authenticated;
      case 'loading':
        return AuthStatus.loading;
      case 'error':
        return AuthStatus.error;
      case 'requires_email_verification':
        return AuthStatus.requiresEmailVerification;
      case 'requires_profile_setup':
        return AuthStatus.requiresProfileSetup;
      default:
        return AuthStatus.unauthenticated; // Default to unauthenticated for safety
    }
  }
}

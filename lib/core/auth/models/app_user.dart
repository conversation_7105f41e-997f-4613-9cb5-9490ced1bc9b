import 'package:firebase_auth/firebase_auth.dart';

import '../../enums/auth_enums.dart';
import '../../../features/chat/enums/chat_permission.dart';
import '../enums/user_status.dart';
import 'user_preferences.dart';

/// Unified model representing a complete user in the Scholara app
/// Combines Firebase Auth data with profile and preference information
class AppUser {
  // Firebase Auth fields
  /// Unique identifier for the user (Firebase UID)
  final String id;

  /// User's email address
  final String email;

  /// User's display name from Firebase Auth
  final String? displayName;

  /// User's profile photo URL from Firebase Auth
  final String? photoUrl;

  /// Whether the user's email is verified
  final bool emailVerified;

  /// When the user account was created
  final DateTime? createdAt;

  /// When the user last signed in
  final DateTime? lastSignInAt;

  /// User's phone number (optional)
  final String? phoneNumber;

  // Profile fields
  /// User's full name (separate from displayName for profile purposes)
  final String? fullName;

  /// Type of user (student, parent, teacher, admin, other)
  final UserType userType;

  /// Current status of the user account
  final UserStatus status;

  /// ID of the user's primary classroom
  final String? primaryClassId;

  /// User's profile image URL (may differ from photoUrl)
  final String? profileImageUrl;

  /// User's grade level (for students)
  final String? grade;

  /// User's school name
  final String? school;

  /// Student ID (for students)
  final String? studentId;

  /// User's bio or description
  final String? bio;

  /// List of subjects the user is associated with
  final List<String> subjects;

  /// User's date of birth
  final DateTime? dateOfBirth;

  /// User's address
  final String? address;

  /// Emergency contact information
  final String? emergencyContact;
  final String? emergencyContactPhone;

  /// Parent/Guardian information
  final String? parentGuardianName;
  final String? parentGuardianPhone;
  final String? parentGuardianEmail;

  // Chat-related fields
  /// Chat permission level for the user
  final ChatPermission chatPermission;

  /// Whether the user is available for chat
  final bool isChatAvailable;

  /// List of user IDs that this user has approved for chat
  final List<String> approvedChatUsers;

  // App-specific fields
  /// User preferences and settings
  final UserPreferences preferences;

  /// When the profile was last updated
  final DateTime? updatedAt;

  /// Whether the user profile is active
  final bool isActive;

  /// Additional metadata
  final Map<String, dynamic>? metadata;

  const AppUser({
    required this.id,
    required this.email,
    this.displayName,
    this.photoUrl,
    required this.emailVerified,
    this.createdAt,
    this.lastSignInAt,
    this.phoneNumber,
    // Profile fields with defaults
    this.fullName,
    this.userType = UserType.student,
    this.status = UserStatus.pending,
    this.primaryClassId,
    this.profileImageUrl,
    this.grade,
    this.school,
    this.studentId,
    this.bio,
    this.subjects = const [],
    this.dateOfBirth,
    this.address,
    this.emergencyContact,
    this.emergencyContactPhone,
    this.parentGuardianName,
    this.parentGuardianPhone,
    this.parentGuardianEmail,
    // Chat fields with defaults
    this.chatPermission = ChatPermission.canOnlyRespond,
    this.isChatAvailable = true,
    this.approvedChatUsers = const [],
    // App fields with defaults
    UserPreferences? preferences,
    this.updatedAt,
    this.isActive = true,
    this.metadata,
  }) : preferences = preferences ?? const UserPreferences();

  /// Create an AppUser from Firebase User (basic auth data only)
  factory AppUser.fromFirebaseUser(User firebaseUser) {
    return AppUser(
      id: firebaseUser.uid,
      email: firebaseUser.email ?? '',
      displayName: firebaseUser.displayName,
      photoUrl: firebaseUser.photoURL,
      emailVerified: firebaseUser.emailVerified,
      createdAt: firebaseUser.metadata.creationTime,
      lastSignInAt: firebaseUser.metadata.lastSignInTime,
      phoneNumber: firebaseUser.phoneNumber,
      // Use displayName as fallback for fullName
      fullName: firebaseUser.displayName,
      // Use photoURL as fallback for profileImageUrl
      profileImageUrl: firebaseUser.photoURL,
      // Default status based on email verification
      status: firebaseUser.emailVerified
          ? UserStatus.profileIncomplete
          : UserStatus.emailVerificationRequired,
    );
  }

  /// Create AppUser from JSON (for local storage and Firestore)
  factory AppUser.fromJson(Map<String, dynamic> json) {
    return AppUser(
      id: json['id'] as String,
      email: json['email'] as String,
      displayName: json['displayName'] as String?,
      photoUrl: json['photoUrl'] as String?,
      emailVerified: json['emailVerified'] as bool? ?? false,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'] as String)
          : null,
      lastSignInAt: json['lastSignInAt'] != null
          ? DateTime.parse(json['lastSignInAt'] as String)
          : null,
      phoneNumber: json['phoneNumber'] as String?,
      // Profile fields
      fullName: json['fullName'] as String?,
      userType: UserTypeExtension.fromString(
        json['userType'] as String? ?? 'student',
      ),
      status: UserStatusExtension.fromString(
        json['status'] as String? ?? 'pending',
      ),
      primaryClassId: json['primaryClassId'] as String?,
      profileImageUrl: json['profileImageUrl'] as String?,
      grade: json['grade'] as String?,
      school: json['school'] as String?,
      studentId: json['studentId'] as String?,
      bio: json['bio'] as String?,
      subjects:
          (json['subjects'] as List<dynamic>?)?.cast<String>() ?? const [],
      dateOfBirth: json['dateOfBirth'] != null
          ? DateTime.parse(json['dateOfBirth'] as String)
          : null,
      address: json['address'] as String?,
      emergencyContact: json['emergencyContact'] as String?,
      emergencyContactPhone: json['emergencyContactPhone'] as String?,
      parentGuardianName: json['parentGuardianName'] as String?,
      parentGuardianPhone: json['parentGuardianPhone'] as String?,
      parentGuardianEmail: json['parentGuardianEmail'] as String?,
      // Chat fields
      chatPermission: ChatPermissionExtension.fromString(
        json['chatPermission'] as String? ?? 'can_only_respond',
      ),
      isChatAvailable: json['isChatAvailable'] as bool? ?? true,
      approvedChatUsers:
          (json['approvedChatUsers'] as List<dynamic>?)?.cast<String>() ??
          const [],
      // App fields
      preferences: json['preferences'] != null
          ? UserPreferences.fromJson(
              json['preferences'] as Map<String, dynamic>,
            )
          : const UserPreferences(),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
      isActive: json['isActive'] as bool? ?? true,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Convert AppUser to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'displayName': displayName,
      'photoUrl': photoUrl,
      'emailVerified': emailVerified,
      'createdAt': createdAt?.toIso8601String(),
      'lastSignInAt': lastSignInAt?.toIso8601String(),
      'phoneNumber': phoneNumber,
      // Profile fields
      'fullName': fullName,
      'userType': userType.value,
      'status': status.value,
      'primaryClassId': primaryClassId,
      'profileImageUrl': profileImageUrl,
      'grade': grade,
      'school': school,
      'studentId': studentId,
      'bio': bio,
      'subjects': subjects,
      'dateOfBirth': dateOfBirth?.toIso8601String(),
      'address': address,
      'emergencyContact': emergencyContact,
      'emergencyContactPhone': emergencyContactPhone,
      'parentGuardianName': parentGuardianName,
      'parentGuardianPhone': parentGuardianPhone,
      'parentGuardianEmail': parentGuardianEmail,
      // Chat fields
      'chatPermission': chatPermission.value,
      'isChatAvailable': isChatAvailable,
      'approvedChatUsers': approvedChatUsers,
      // App fields
      'preferences': preferences.toJson(),
      'updatedAt': updatedAt?.toIso8601String(),
      'isActive': isActive,
      'metadata': metadata,
    };
  }

  /// Get the user's display name (prioritizes fullName over displayName)
  String get effectiveDisplayName {
    return fullName?.isNotEmpty == true
        ? fullName!
        : displayName?.isNotEmpty == true
        ? displayName!
        : email.split('@').first;
  }

  /// Get the user's profile image URL (prioritizes profileImageUrl over photoUrl)
  String? get effectiveProfileImageUrl {
    return profileImageUrl?.isNotEmpty == true
        ? profileImageUrl
        : photoUrl?.isNotEmpty == true
        ? photoUrl
        : null;
  }

  /// Check if the user has completed their profile
  bool get hasCompletedProfile {
    return fullName?.isNotEmpty == true && userType != UserType.student ||
        (grade?.isNotEmpty == true);
  }

  /// Check if the user can access the main app
  bool get canAccessApp {
    return emailVerified && status.canAccessApp && isActive;
  }

  /// Check if the user needs to complete profile setup
  bool get needsProfileSetup {
    return !hasCompletedProfile || status == UserStatus.profileIncomplete;
  }

  /// Check if the user needs email verification
  bool get needsEmailVerification {
    return !emailVerified || status == UserStatus.emailVerificationRequired;
  }

  /// Create a copy of this AppUser with updated fields
  AppUser copyWith({
    String? id,
    String? email,
    String? displayName,
    String? photoUrl,
    bool? emailVerified,
    DateTime? createdAt,
    DateTime? lastSignInAt,
    String? phoneNumber,
    // Profile fields
    String? fullName,
    UserType? userType,
    UserStatus? status,
    String? primaryClassId,
    String? profileImageUrl,
    String? grade,
    String? school,
    String? studentId,
    String? bio,
    List<String>? subjects,
    DateTime? dateOfBirth,
    String? address,
    String? emergencyContact,
    String? emergencyContactPhone,
    String? parentGuardianName,
    String? parentGuardianPhone,
    String? parentGuardianEmail,
    // Chat fields
    ChatPermission? chatPermission,
    bool? isChatAvailable,
    List<String>? approvedChatUsers,
    // App fields
    UserPreferences? preferences,
    DateTime? updatedAt,
    bool? isActive,
    Map<String, dynamic>? metadata,
  }) {
    return AppUser(
      id: id ?? this.id,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      photoUrl: photoUrl ?? this.photoUrl,
      emailVerified: emailVerified ?? this.emailVerified,
      createdAt: createdAt ?? this.createdAt,
      lastSignInAt: lastSignInAt ?? this.lastSignInAt,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      // Profile fields
      fullName: fullName ?? this.fullName,
      userType: userType ?? this.userType,
      status: status ?? this.status,
      primaryClassId: primaryClassId ?? this.primaryClassId,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      grade: grade ?? this.grade,
      school: school ?? this.school,
      studentId: studentId ?? this.studentId,
      bio: bio ?? this.bio,
      subjects: subjects ?? this.subjects,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      address: address ?? this.address,
      emergencyContact: emergencyContact ?? this.emergencyContact,
      emergencyContactPhone:
          emergencyContactPhone ?? this.emergencyContactPhone,
      parentGuardianName: parentGuardianName ?? this.parentGuardianName,
      parentGuardianPhone: parentGuardianPhone ?? this.parentGuardianPhone,
      parentGuardianEmail: parentGuardianEmail ?? this.parentGuardianEmail,
      // Chat fields
      chatPermission: chatPermission ?? this.chatPermission,
      isChatAvailable: isChatAvailable ?? this.isChatAvailable,
      approvedChatUsers: approvedChatUsers ?? this.approvedChatUsers,
      // App fields
      preferences: preferences ?? this.preferences,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppUser &&
        other.id == id &&
        other.email == email &&
        other.emailVerified == emailVerified &&
        other.userType == userType &&
        other.status == status;
  }

  @override
  int get hashCode {
    return Object.hash(id, email, emailVerified, userType, status);
  }

  @override
  String toString() {
    return 'AppUser(id: $id, email: $email, fullName: $fullName, userType: $userType, status: $status)';
  }
}

import 'package:flutter/material.dart';

/// Model representing user preferences and settings
class UserPreferences {
  /// Theme mode preference
  final ThemeMode themeMode;
  
  /// Language preference (locale code)
  final String languageCode;
  
  /// Whether to receive push notifications
  final bool pushNotificationsEnabled;
  
  /// Whether to receive email notifications
  final bool emailNotificationsEnabled;
  
  /// Whether to receive homework reminders
  final bool homeworkRemindersEnabled;
  
  /// Whether to receive announcement notifications
  final bool announcementNotificationsEnabled;
  
  /// Whether to receive chat notifications
  final bool chatNotificationsEnabled;
  
  /// Whether to show profile to other users
  final bool profileVisibilityEnabled;
  
  /// Whether to allow others to find user by email
  final bool discoverableByEmail;
  
  /// Default file upload quality (for images)
  final String defaultUploadQuality;
  
  /// Whether to auto-download files on WiFi
  final bool autoDownloadOnWifi;
  
  /// Whether to auto-download files on mobile data
  final bool autoDownloadOnMobile;
  
  /// Additional custom preferences
  final Map<String, dynamic> customPreferences;

  const UserPreferences({
    this.themeMode = ThemeMode.system,
    this.languageCode = 'en',
    this.pushNotificationsEnabled = true,
    this.emailNotificationsEnabled = true,
    this.homeworkRemindersEnabled = true,
    this.announcementNotificationsEnabled = true,
    this.chatNotificationsEnabled = true,
    this.profileVisibilityEnabled = true,
    this.discoverableByEmail = false,
    this.defaultUploadQuality = 'medium',
    this.autoDownloadOnWifi = true,
    this.autoDownloadOnMobile = false,
    this.customPreferences = const {},
  });

  /// Create default preferences
  factory UserPreferences.defaultPreferences() {
    return const UserPreferences();
  }

  /// Create UserPreferences from JSON
  factory UserPreferences.fromJson(Map<String, dynamic> json) {
    return UserPreferences(
      themeMode: _themeModeFromString(json['themeMode'] as String? ?? 'system'),
      languageCode: json['languageCode'] as String? ?? 'en',
      pushNotificationsEnabled: json['pushNotificationsEnabled'] as bool? ?? true,
      emailNotificationsEnabled: json['emailNotificationsEnabled'] as bool? ?? true,
      homeworkRemindersEnabled: json['homeworkRemindersEnabled'] as bool? ?? true,
      announcementNotificationsEnabled: json['announcementNotificationsEnabled'] as bool? ?? true,
      chatNotificationsEnabled: json['chatNotificationsEnabled'] as bool? ?? true,
      profileVisibilityEnabled: json['profileVisibilityEnabled'] as bool? ?? true,
      discoverableByEmail: json['discoverableByEmail'] as bool? ?? false,
      defaultUploadQuality: json['defaultUploadQuality'] as String? ?? 'medium',
      autoDownloadOnWifi: json['autoDownloadOnWifi'] as bool? ?? true,
      autoDownloadOnMobile: json['autoDownloadOnMobile'] as bool? ?? false,
      customPreferences: json['customPreferences'] as Map<String, dynamic>? ?? const {},
    );
  }

  /// Convert UserPreferences to JSON
  Map<String, dynamic> toJson() {
    return {
      'themeMode': _themeModeToString(themeMode),
      'languageCode': languageCode,
      'pushNotificationsEnabled': pushNotificationsEnabled,
      'emailNotificationsEnabled': emailNotificationsEnabled,
      'homeworkRemindersEnabled': homeworkRemindersEnabled,
      'announcementNotificationsEnabled': announcementNotificationsEnabled,
      'chatNotificationsEnabled': chatNotificationsEnabled,
      'profileVisibilityEnabled': profileVisibilityEnabled,
      'discoverableByEmail': discoverableByEmail,
      'defaultUploadQuality': defaultUploadQuality,
      'autoDownloadOnWifi': autoDownloadOnWifi,
      'autoDownloadOnMobile': autoDownloadOnMobile,
      'customPreferences': customPreferences,
    };
  }

  /// Create a copy of this UserPreferences with updated fields
  UserPreferences copyWith({
    ThemeMode? themeMode,
    String? languageCode,
    bool? pushNotificationsEnabled,
    bool? emailNotificationsEnabled,
    bool? homeworkRemindersEnabled,
    bool? announcementNotificationsEnabled,
    bool? chatNotificationsEnabled,
    bool? profileVisibilityEnabled,
    bool? discoverableByEmail,
    String? defaultUploadQuality,
    bool? autoDownloadOnWifi,
    bool? autoDownloadOnMobile,
    Map<String, dynamic>? customPreferences,
  }) {
    return UserPreferences(
      themeMode: themeMode ?? this.themeMode,
      languageCode: languageCode ?? this.languageCode,
      pushNotificationsEnabled: pushNotificationsEnabled ?? this.pushNotificationsEnabled,
      emailNotificationsEnabled: emailNotificationsEnabled ?? this.emailNotificationsEnabled,
      homeworkRemindersEnabled: homeworkRemindersEnabled ?? this.homeworkRemindersEnabled,
      announcementNotificationsEnabled: announcementNotificationsEnabled ?? this.announcementNotificationsEnabled,
      chatNotificationsEnabled: chatNotificationsEnabled ?? this.chatNotificationsEnabled,
      profileVisibilityEnabled: profileVisibilityEnabled ?? this.profileVisibilityEnabled,
      discoverableByEmail: discoverableByEmail ?? this.discoverableByEmail,
      defaultUploadQuality: defaultUploadQuality ?? this.defaultUploadQuality,
      autoDownloadOnWifi: autoDownloadOnWifi ?? this.autoDownloadOnWifi,
      autoDownloadOnMobile: autoDownloadOnMobile ?? this.autoDownloadOnMobile,
      customPreferences: customPreferences ?? this.customPreferences,
    );
  }

  /// Helper method to convert ThemeMode to string
  static String _themeModeToString(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.light:
        return 'light';
      case ThemeMode.dark:
        return 'dark';
      case ThemeMode.system:
        return 'system';
    }
  }

  /// Helper method to convert string to ThemeMode
  static ThemeMode _themeModeFromString(String value) {
    switch (value.toLowerCase()) {
      case 'light':
        return ThemeMode.light;
      case 'dark':
        return ThemeMode.dark;
      case 'system':
      default:
        return ThemeMode.system;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserPreferences &&
        other.themeMode == themeMode &&
        other.languageCode == languageCode &&
        other.pushNotificationsEnabled == pushNotificationsEnabled &&
        other.emailNotificationsEnabled == emailNotificationsEnabled &&
        other.homeworkRemindersEnabled == homeworkRemindersEnabled &&
        other.announcementNotificationsEnabled == announcementNotificationsEnabled &&
        other.chatNotificationsEnabled == chatNotificationsEnabled &&
        other.profileVisibilityEnabled == profileVisibilityEnabled &&
        other.discoverableByEmail == discoverableByEmail &&
        other.defaultUploadQuality == defaultUploadQuality &&
        other.autoDownloadOnWifi == autoDownloadOnWifi &&
        other.autoDownloadOnMobile == autoDownloadOnMobile;
  }

  @override
  int get hashCode {
    return Object.hash(
      themeMode,
      languageCode,
      pushNotificationsEnabled,
      emailNotificationsEnabled,
      homeworkRemindersEnabled,
      announcementNotificationsEnabled,
      chatNotificationsEnabled,
      profileVisibilityEnabled,
      discoverableByEmail,
      defaultUploadQuality,
      autoDownloadOnWifi,
      autoDownloadOnMobile,
    );
  }

  @override
  String toString() {
    return 'UserPreferences(themeMode: $themeMode, languageCode: $languageCode, notifications: ${pushNotificationsEnabled ? 'enabled' : 'disabled'})';
  }
}

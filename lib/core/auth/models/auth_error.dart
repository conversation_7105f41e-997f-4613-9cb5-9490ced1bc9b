import '../../enums/auth_enums.dart';

/// Model representing authentication errors with detailed information
class AuthError {
  /// Type of authentication error
  final AuthErrorType type;
  
  /// Human-readable error message
  final String message;
  
  /// Technical error code (optional)
  final String? code;
  
  /// Additional error details (optional)
  final String? details;
  
  /// The operation that caused the error
  final AuthOperation? operation;
  
  /// When the error occurred
  final DateTime timestamp;
  
  /// Whether this error can be retried
  final bool canRetry;
  
  /// Additional metadata about the error
  final Map<String, dynamic>? metadata;

  const AuthError({
    required this.type,
    required this.message,
    this.code,
    this.details,
    this.operation,
    required this.timestamp,
    this.canRetry = true,
    this.metadata,
  });

  /// Create an AuthError from an exception
  factory AuthError.fromException(
    Exception exception, {
    AuthOperation? operation,
    Map<String, dynamic>? metadata,
  }) {
    final errorString = exception.toString();
    
    // Try to parse Firebase Auth error codes
    AuthErrorType type = AuthErrorType.unknown;
    String message = 'An unexpected error occurred. Please try again.';
    String? code;
    bool canRetry = true;

    if (errorString.contains('firebase_auth')) {
      // Parse Firebase Auth errors
      if (errorString.contains('invalid-email')) {
        type = AuthErrorType.invalidEmail;
        message = 'Please enter a valid email address.';
        canRetry = false;
      } else if (errorString.contains('weak-password')) {
        type = AuthErrorType.weakPassword;
        message = 'Password should be at least 6 characters long.';
        canRetry = false;
      } else if (errorString.contains('email-already-in-use')) {
        type = AuthErrorType.emailAlreadyInUse;
        message = 'An account already exists with this email address.';
        canRetry = false;
      } else if (errorString.contains('user-not-found')) {
        type = AuthErrorType.userNotFound;
        message = 'No account found with this email address.';
        canRetry = false;
      } else if (errorString.contains('wrong-password')) {
        type = AuthErrorType.wrongPassword;
        message = 'Incorrect password. Please try again.';
        canRetry = false;
      } else if (errorString.contains('user-disabled')) {
        type = AuthErrorType.userDisabled;
        message = 'This account has been disabled. Please contact support.';
        canRetry = false;
      } else if (errorString.contains('too-many-requests')) {
        type = AuthErrorType.tooManyRequests;
        message = 'Too many failed attempts. Please try again later.';
        canRetry = true;
      } else if (errorString.contains('network-request-failed')) {
        type = AuthErrorType.networkError;
        message = 'Network error. Please check your connection and try again.';
        canRetry = true;
      } else if (errorString.contains('invalid-credential')) {
        type = AuthErrorType.invalidCredential;
        message = 'Invalid credentials. Please check your email and password.';
        canRetry = false;
      }
      
      // Extract error code if present
      final codeMatch = RegExp(r'\[([^\]]+)\]').firstMatch(errorString);
      if (codeMatch != null) {
        code = codeMatch.group(1);
      }
    }

    return AuthError(
      type: type,
      message: message,
      code: code,
      details: errorString,
      operation: operation,
      timestamp: DateTime.now(),
      canRetry: canRetry,
      metadata: metadata,
    );
  }

  /// Create a custom AuthError
  factory AuthError.custom({
    required AuthErrorType type,
    required String customMessage,
    String? code,
    String? details,
    AuthOperation? operation,
    bool canRetry = true,
    Map<String, dynamic>? metadata,
  }) {
    return AuthError(
      type: type,
      message: customMessage,
      code: code,
      details: details,
      operation: operation,
      timestamp: DateTime.now(),
      canRetry: canRetry,
      metadata: metadata,
    );
  }

  /// Create a network error
  factory AuthError.network({
    AuthOperation? operation,
    Map<String, dynamic>? metadata,
  }) {
    return AuthError(
      type: AuthErrorType.networkError,
      message: 'Network error. Please check your connection and try again.',
      operation: operation,
      timestamp: DateTime.now(),
      canRetry: true,
      metadata: metadata,
    );
  }

  /// Create an unknown error
  factory AuthError.unknown({
    String? customMessage,
    AuthOperation? operation,
    Map<String, dynamic>? metadata,
  }) {
    return AuthError(
      type: AuthErrorType.unknown,
      message: customMessage ?? 'An unexpected error occurred. Please try again.',
      operation: operation,
      timestamp: DateTime.now(),
      canRetry: true,
      metadata: metadata,
    );
  }

  /// Get a user-friendly error message based on the operation context
  String get contextualMessage {
    final baseMessage = message;
    
    if (operation == null) return baseMessage;
    
    switch (operation!) {
      case AuthOperation.signIn:
        if (type == AuthErrorType.userNotFound || type == AuthErrorType.wrongPassword) {
          return 'Invalid email or password. Please check your credentials and try again.';
        }
        return 'Sign in failed. $baseMessage';
      
      case AuthOperation.signUp:
        if (type == AuthErrorType.emailAlreadyInUse) {
          return 'An account with this email already exists. Try signing in instead.';
        }
        return 'Account creation failed. $baseMessage';
      
      case AuthOperation.resetPassword:
        if (type == AuthErrorType.userNotFound) {
          return 'No account found with this email address.';
        }
        return 'Password reset failed. $baseMessage';
      
      case AuthOperation.verifyEmail:
        return 'Email verification failed. $baseMessage';
      
      case AuthOperation.updateProfile:
        return 'Profile update failed. $baseMessage';
      
      case AuthOperation.updatePassword:
        return 'Password update failed. $baseMessage';
      
      case AuthOperation.deleteAccount:
        return 'Account deletion failed. $baseMessage';
      
      case AuthOperation.signOut:
        return 'Sign out failed. $baseMessage';
      
      case AuthOperation.refreshToken:
        return 'Session refresh failed. Please sign in again.';
    }
  }

  /// Check if this error is related to network connectivity
  bool get isNetworkError {
    return type == AuthErrorType.networkError;
  }

  /// Check if this error is related to invalid credentials
  bool get isCredentialError {
    return type == AuthErrorType.invalidEmail ||
           type == AuthErrorType.wrongPassword ||
           type == AuthErrorType.userNotFound ||
           type == AuthErrorType.invalidCredential;
  }

  /// Check if this error requires user action to resolve
  bool get requiresUserAction {
    return type == AuthErrorType.emailAlreadyInUse ||
           type == AuthErrorType.weakPassword ||
           type == AuthErrorType.invalidEmail ||
           type == AuthErrorType.userDisabled;
  }

  /// Convert AuthError to JSON
  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'message': message,
      'code': code,
      'details': details,
      'operation': operation?.name,
      'timestamp': timestamp.toIso8601String(),
      'canRetry': canRetry,
      'metadata': metadata,
    };
  }

  /// Create AuthError from JSON
  factory AuthError.fromJson(Map<String, dynamic> json) {
    return AuthError(
      type: AuthErrorType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => AuthErrorType.unknown,
      ),
      message: json['message'] as String,
      code: json['code'] as String?,
      details: json['details'] as String?,
      operation: json['operation'] != null
          ? AuthOperation.values.firstWhere(
              (e) => e.name == json['operation'],
              orElse: () => AuthOperation.signIn,
            )
          : null,
      timestamp: DateTime.parse(json['timestamp'] as String),
      canRetry: json['canRetry'] as bool? ?? true,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthError &&
        other.type == type &&
        other.message == message &&
        other.code == code &&
        other.operation == operation;
  }

  @override
  int get hashCode {
    return Object.hash(type, message, code, operation);
  }

  @override
  String toString() {
    return 'AuthError(type: $type, message: $message, operation: $operation)';
  }
}

import '../../enums/auth_enums.dart';
import '../enums/auth_status.dart';
import 'app_user.dart';
import 'auth_error.dart';

/// Model representing the complete authentication state of the app
class AuthState {
  /// Current authentication status
  final AuthStatus status;
  
  /// Current authenticated user (null if not authenticated)
  final AppUser? user;
  
  /// Current authentication error (null if no error)
  final AuthError? error;
  
  /// Whether an authentication operation is in progress
  final bool isLoading;
  
  /// The current operation being performed
  final AuthOperation? currentOperation;
  
  /// URL to redirect to after successful authentication
  final String? redirectUrl;
  
  /// When the state was last updated
  final DateTime lastUpdated;

  const AuthState({
    required this.status,
    this.user,
    this.error,
    this.isLoading = false,
    this.currentOperation,
    this.redirectUrl,
    required this.lastUpdated,
  });

  /// Create an initial/unauthenticated state
  factory AuthState.initial() {
    return AuthState(
      status: AuthStatus.unauthenticated,
      lastUpdated: DateTime.now(),
    );
  }

  /// Create a loading state
  factory AuthState.loading({
    AuthOperation? operation,
    AppUser? currentUser,
    String? redirectUrl,
  }) {
    return AuthState(
      status: AuthStatus.loading,
      user: currentUser,
      isLoading: true,
      currentOperation: operation,
      redirectUrl: redirectUrl,
      lastUpdated: DateTime.now(),
    );
  }

  /// Create an authenticated state
  factory AuthState.authenticated(AppUser user, {String? redirectUrl}) {
    return AuthState(
      status: AuthStatus.authenticated,
      user: user,
      redirectUrl: redirectUrl,
      lastUpdated: DateTime.now(),
    );
  }

  /// Create an unauthenticated state
  factory AuthState.unauthenticated({String? redirectUrl}) {
    return AuthState(
      status: AuthStatus.unauthenticated,
      redirectUrl: redirectUrl,
      lastUpdated: DateTime.now(),
    );
  }

  /// Create a state requiring email verification
  factory AuthState.requiresEmailVerification(AppUser user, {String? redirectUrl}) {
    return AuthState(
      status: AuthStatus.requiresEmailVerification,
      user: user,
      redirectUrl: redirectUrl,
      lastUpdated: DateTime.now(),
    );
  }

  /// Create a state requiring profile setup
  factory AuthState.requiresProfileSetup(AppUser user, {String? redirectUrl}) {
    return AuthState(
      status: AuthStatus.requiresProfileSetup,
      user: user,
      redirectUrl: redirectUrl,
      lastUpdated: DateTime.now(),
    );
  }

  /// Create an error state
  factory AuthState.error(
    AuthError error, {
    AppUser? currentUser,
    String? redirectUrl,
  }) {
    return AuthState(
      status: AuthStatus.error,
      user: currentUser,
      error: error,
      redirectUrl: redirectUrl,
      lastUpdated: DateTime.now(),
    );
  }

  /// Check if user is authenticated (regardless of additional requirements)
  bool get isAuthenticated => status.isAuthenticated;

  /// Check if user can access the main app
  bool get canAccessApp => status.canAccessApp;

  /// Check if there's an error
  bool get hasError => error != null;

  /// Check if the state is loading
  bool get isLoadingState => status.isLoading || isLoading;

  /// Check if this status requires user action
  bool get requiresAction => status.requiresAction;

  /// Get a user-friendly message for the current state
  String get message {
    switch (status) {
      case AuthStatus.unauthenticated:
        return 'Please sign in to continue';
      case AuthStatus.authenticated:
        return 'Welcome back, ${user?.effectiveDisplayName ?? 'User'}!';
      case AuthStatus.loading:
        return _getLoadingMessage();
      case AuthStatus.error:
        return error?.message ?? 'An error occurred';
      case AuthStatus.requiresEmailVerification:
        return 'Please verify your email address to continue';
      case AuthStatus.requiresProfileSetup:
        return 'Please complete your profile setup';
    }
  }

  /// Get loading message based on current operation
  String _getLoadingMessage() {
    switch (currentOperation) {
      case AuthOperation.signIn:
        return 'Signing in...';
      case AuthOperation.signUp:
        return 'Creating account...';
      case AuthOperation.signOut:
        return 'Signing out...';
      case AuthOperation.resetPassword:
        return 'Sending password reset email...';
      case AuthOperation.verifyEmail:
        return 'Verifying email...';
      case AuthOperation.updateProfile:
        return 'Updating profile...';
      case AuthOperation.updatePassword:
        return 'Updating password...';
      case AuthOperation.deleteAccount:
        return 'Deleting account...';
      case AuthOperation.refreshToken:
        return 'Refreshing session...';
      case null:
        return 'Loading...';
    }
  }

  /// Create a copy of this AuthState with updated fields
  AuthState copyWith({
    AuthStatus? status,
    AppUser? user,
    AuthError? error,
    bool? isLoading,
    AuthOperation? currentOperation,
    String? redirectUrl,
    DateTime? lastUpdated,
    bool clearError = false,
    bool clearUser = false,
    bool clearRedirectUrl = false,
  }) {
    return AuthState(
      status: status ?? this.status,
      user: clearUser ? null : (user ?? this.user),
      error: clearError ? null : (error ?? this.error),
      isLoading: isLoading ?? this.isLoading,
      currentOperation: currentOperation ?? this.currentOperation,
      redirectUrl: clearRedirectUrl ? null : (redirectUrl ?? this.redirectUrl),
      lastUpdated: lastUpdated ?? DateTime.now(),
    );
  }

  /// Convert AuthState to JSON
  Map<String, dynamic> toJson() {
    return {
      'status': status.value,
      'user': user?.toJson(),
      'error': error?.toJson(),
      'isLoading': isLoading,
      'currentOperation': currentOperation?.name,
      'redirectUrl': redirectUrl,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  /// Create AuthState from JSON
  factory AuthState.fromJson(Map<String, dynamic> json) {
    return AuthState(
      status: AuthStatusExtension.fromString(json['status'] as String? ?? 'unauthenticated'),
      user: json['user'] != null 
          ? AppUser.fromJson(json['user'] as Map<String, dynamic>)
          : null,
      error: json['error'] != null
          ? AuthError.fromJson(json['error'] as Map<String, dynamic>)
          : null,
      isLoading: json['isLoading'] as bool? ?? false,
      currentOperation: json['currentOperation'] != null
          ? AuthOperation.values.firstWhere(
              (e) => e.name == json['currentOperation'],
              orElse: () => AuthOperation.signIn,
            )
          : null,
      redirectUrl: json['redirectUrl'] as String?,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthState &&
        other.status == status &&
        other.user == user &&
        other.error == error &&
        other.isLoading == isLoading &&
        other.currentOperation == currentOperation &&
        other.redirectUrl == redirectUrl;
  }

  @override
  int get hashCode {
    return Object.hash(
      status,
      user,
      error,
      isLoading,
      currentOperation,
      redirectUrl,
    );
  }

  @override
  String toString() {
    return 'AuthState(status: $status, user: ${user?.effectiveDisplayName}, error: $error, isLoading: $isLoading)';
  }
}

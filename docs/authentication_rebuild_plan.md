# Authentication & User Management Rebuild Plan

## Overview

This document outlines a comprehensive plan to rebuild the authentication and user management foundation for the Scholara Student app. The current implementation has several architectural issues that need to be addressed for better scalability, maintainability, and user experience.

## Current Issues Identified

### 1. **Fragmented User Data Model**

- User data is split between `UserModel` (Firebase Auth) and `ProfileModel` (Firestore)
- Complex merging logic in `currentUserWithProfileProvider`
- Inconsistent data synchronization between Firebase Auth and Firestore
- Redundant fields across models

### 2. **Authentication State Management**

- Complex provider hierarchy with multiple auth-related providers
- Inconsistent error handling across different auth operations
- Manual state synchronization between local storage and providers
- No proper session management or token refresh handling

### 3. **Route Protection Issues**

- Basic route guard implementation without proper loading states
- No role-based access control
- Missing deep link handling for authenticated routes
- Inconsistent redirect logic

### 4. **Local Storage & Persistence**

- Manual data persistence without proper sync mechanisms
- No offline-first approach
- Inconsistent data clearing on logout
- No data migration strategy for app updates

### 5. **User Profile Management**

- Separate profile creation flow not integrated with auth
- No proper onboarding flow for new users
- Missing profile completion validation
- No profile image upload integration

## Proposed Solution Architecture

### Phase 1: Core Authentication Foundation

#### 1.1 Unified User Model

- **Create single `AppUser` model** that combines Firebase Auth and profile data
- **Implement proper data mapping** between Firebase User and Firestore profile
- **Add user roles and permissions** system
- **Create user status enum** (pending, active, suspended, etc.)

#### 1.2 Authentication Service Layer

- **Rebuild `AuthService`** with proper error handling and logging
- **Simple session management** with Firebase Auth state persistence
- **Clean authentication flow** without complex token handling

#### 1.3 State Management Refactor

- **Single `AuthNotifier`** using Riverpod StateNotifier
- **Unified auth state** with proper loading, error, and success states
- **Automatic state persistence** with secure storage
- **Real-time auth state synchronization**

### Phase 2: User Profile Management

#### 2.1 Basic Profile System

- **Profile view page** displaying user information
- **Profile edit page** for updating user data
- **Dummy data integration** for testing (TODO: Remove dummy data later)
- **Basic profile validation** for required fields

#### 2.2 User Preferences

- **Centralized preferences management**
- **Theme and language settings**
- **Basic app settings**

### Phase 3: Enhanced Route Management & Security

#### 3.1 Enhanced Route Protection

- **Role-based route guards** with granular permissions
- **Deep link handling** with auth state validation
- **Proper loading states** during auth checks
- **Redirect management** with return URL support

#### 3.2 Basic Security Enhancements

- **Email verification** enforcement
- **Password strength validation**
- **Basic security logging**

### Phase 4: Data Persistence & Cleanup

#### 4.1 Local Data Management

- **Improved local storage** for user data
- **Data synchronization** between local and remote
- **Proper data clearing** on logout

#### 4.2 Migration & Cleanup

- **Remove old authentication** implementation
- **Clean up unused providers** and models
- **Update all auth-dependent** features

## Implementation Details

### New File Structure

```
lib/
├── core/
│   ├── auth/
│   │   ├── models/
│   │   │   ├── app_user.dart
│   │   │   ├── auth_state.dart
│   │   │   ├── user_session.dart
│   │   │   └── auth_error.dart
│   │   ├── services/
│   │   │   ├── auth_service.dart
│   │   │   ├── session_service.dart
│   │   │   ├── profile_service.dart
│   │   │   └── security_service.dart
│   │   ├── providers/
│   │   │   ├── auth_provider.dart
│   │   │   ├── user_provider.dart
│   │   │   └── session_provider.dart
│   │   ├── repositories/
│   │   │   ├── auth_repository.dart
│   │   │   ├── user_repository.dart
│   │   │   └── profile_repository.dart
│   │   └── guards/
│   │       ├── auth_guard.dart
│   │       ├── role_guard.dart
│   │       └── route_guard.dart
│   ├── storage/
│   │   ├── secure_storage.dart
│   │   ├── local_database.dart
│   │   └── sync_service.dart
│   └── constants/
│       ├── auth_constants.dart
│       ├── user_roles.dart
│       └── permissions.dart
├── features/
│   ├── auth/
│   │   ├── screens/
│   │   │   ├── login/
│   │   │   ├── signup/
│   │   │   ├── forgot_password/
│   │   │   └── email_verification/
│   │   └── widgets/
│   ├── onboarding/
│   │   ├── screens/
│   │   │   ├── welcome/
│   │   │   ├── profile_setup/
│   │   │   └── permissions/
│   │   └── widgets/
│   └── profile/
│       ├── screens/
│       │   ├── profile_view/
│       │   ├── profile_edit/
│       │   └── settings/
│       └── widgets/
```

### Key Models

#### AppUser Model

```dart
class AppUser {
  final String id;
  final String email;
  final String? displayName;
  final bool emailVerified;
  final UserRole role;
  final UserStatus status;
  final Profile? profile;
  final UserPreferences preferences;
  final DateTime createdAt;
  final DateTime lastLoginAt;
  final List<String> permissions;
}
```

#### AuthState Model

```dart
class AuthState {
  final AuthStatus status;
  final AppUser? user;
  final AuthError? error;
  final bool isLoading;
  final String? redirectUrl;
}
```

### Migration Strategy

#### Phase 1: Preparation

1. **Create new auth structure** alongside existing implementation
2. **Implement new models** and services
3. **Add feature flags** to switch between old and new auth
4. **Create data migration scripts**

#### Phase 2: Gradual Migration

1. **Migrate authentication screens** to new system
2. **Update route guards** with new auth providers
3. **Migrate user profile management**
4. **Update all auth-dependent features**

#### Phase 3: Cleanup

1. **Remove old auth implementation**
2. **Clean up unused providers and models**
3. **Update documentation**
4. **Performance optimization**

## Testing Strategy

### Unit Tests

- **Auth service methods** with mock Firebase
- **State management** with provider testing
- **Data models** serialization/deserialization
- **Route guards** with different auth states

### Integration Tests

- **Complete auth flows** (login, signup, logout)
- **Profile creation and updates**
- **Route protection** scenarios
- **Offline/online sync** behavior

### E2E Tests

- **User registration** to dashboard flow
- **Password reset** complete flow
- **Profile setup** wizard
- **Multi-device** session handling

## Success Metrics

### Performance

- **Auth state loading** < 500ms
- **Profile data sync** < 1s
- **Route transitions** < 300ms
- **Offline data access** < 100ms

### Reliability

- **99.9% auth success rate** for valid credentials
- **Zero data loss** during sync operations
- **Proper error recovery** in all scenarios
- **Session persistence** across app restarts

### User Experience

- **Seamless onboarding** flow completion rate > 90%
- **Profile completion** rate > 85%
- **User satisfaction** with auth flow > 4.5/5
- **Support tickets** related to auth < 5% of total

## Implementation Order

### Phase 1: Foundation

- New models and services
- Core auth provider implementation
- Basic auth screens migration

### Phase 2: Profile Management

- Profile view and edit pages
- User preferences integration
- Dummy data integration

### Phase 3: Security & Routes

- Enhanced route protection
- Basic security features implementation
- Improved session management

### Phase 4: Cleanup & Polish

- Data migration and cleanup
- Remove old implementation
- Testing and optimization

## Risk Mitigation

### Technical Risks

- **Data loss during migration**: Implement comprehensive backup system
- **Performance degradation**: Gradual rollout with monitoring
- **Breaking changes**: Feature flags and rollback strategy

### User Experience Risks

- **Forced re-login**: Smooth migration with session preservation
- **Profile data loss**: Robust data validation and recovery
- **Onboarding friction**: A/B testing for optimal flow

## Next Steps

1. **Review and approve** this plan with the development team
2. **Set up development environment** with new structure
3. **Create detailed task breakdown** for each phase
4. **Begin Phase 1 implementation** with core foundation
5. **Set up monitoring and analytics** for migration tracking

---

_This plan provides a solid foundation for rebuilding the authentication and user management system. Each phase builds upon the previous one, ensuring a smooth transition while maintaining app functionality._
